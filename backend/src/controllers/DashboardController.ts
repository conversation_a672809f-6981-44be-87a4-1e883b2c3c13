import { Request, Response } from 'express';
import { CharacterService } from '@services/CharacterService';
import { SwimsuitService } from '@services/SwimsuitService';
import { SkillService } from '@services/SkillService';
import { ItemService } from '@services/ItemService';
import { BromideService } from '@services/BromideService';
import { EventService } from '@services/EventService';
import logger from '@config/logger';

export class DashboardController {
  private characterService: CharacterService;
  private swimsuitsService: SwimsuitService;
  private skillsService: SkillService;
  private itemsService: ItemService;
  private bromidesService: BromideService;
  private eventsService: EventService;

  constructor() {
    this.characterService = new CharacterService();
    this.swimsuitsService = new SwimsuitService();
    this.skillsService = new SkillService();
    this.itemsService = new ItemService();
    this.bromidesService = new BromideService();
    this.eventsService = new EventService();
  }

  /**
   * Get overview data for dashboard - combines multiple resources in a single request
   * This replaces the need for 4 separate API calls in ItemsPage.tsx
   */
  async getOverview(req: Request, res: Response): Promise<void> {
    try {
      logger.info('Fetching dashboard overview data');

      // Fetch all data in parallel for better performance
      const [
        swimsuitsResult,
        accessoriesResult,
        skillsResult,
        bromidesResult
      ] = await Promise.all([
        this.swimsuitsService.getSwimsuits({ limit: 100, page: 1 }),
        this.itemsService.getItemsByCategory('ACCESSORY', { limit: 100, page: 1 }),
        this.skillsService.getSkills({ limit: 100, page: 1 }),
        this.bromidesService.getBromides({ limit: 100, page: 1 })
      ]);

      // Combine all data into a single response
      const overviewData = {
        swimsuits: {
          data: swimsuitsResult.data,
          pagination: swimsuitsResult.pagination
        },
        accessories: {
          data: accessoriesResult.data,
          pagination: accessoriesResult.pagination
        },
        skills: {
          data: skillsResult.data,
          pagination: skillsResult.pagination
        },
        bromides: {
          data: bromidesResult.data,
          pagination: bromidesResult.pagination
        },
        summary: {
          totalSwimsuits: swimsuitsResult.pagination?.total || 0,
          totalAccessories: accessoriesResult.pagination?.total || 0,
          totalSkills: skillsResult.pagination?.total || 0,
          totalBromides: bromidesResult.pagination?.total || 0,
          lastUpdated: new Date().toISOString()
        }
      };

      logger.info('Dashboard overview data fetched successfully', {
        swimsuits: overviewData.swimsuits.data.length,
        accessories: overviewData.accessories.data.length,
        skills: overviewData.skills.data.length,
        bromides: overviewData.bromides.data.length
      });

      res.json({
        success: true,
        data: overviewData,
        message: 'Dashboard overview data retrieved successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error fetching dashboard overview:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch dashboard overview data',
        errorId: `err_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: new Date().toISOString()
      });
    }
  }

  /**
   * Get character statistics for dashboard
   */
  async getCharacterStats(req: Request, res: Response): Promise<void> {
    try {
      logger.info('Fetching character statistics');

      const [
        charactersResult,
        swimsuitsResult
      ] = await Promise.all([
        this.characterService.getCharacters({ limit: 1000, page: 1 }),
        this.swimsuitsService.getSwimsuits({ limit: 1000, page: 1 })
      ]);

      // Calculate statistics
      const characters = charactersResult.data;
      const swimsuits = swimsuitsResult.data;

      const stats = {
        totalCharacters: characters.length,
        totalSwimsuits: swimsuits.length,
        averageSwimsuitsPerCharacter: characters.length > 0 ? (swimsuits.length / characters.length).toFixed(2) : '0',
        charactersByBirthday: this.groupCharactersByMonth(characters),
        swimsuitsByRarity: this.groupSwimsuitssByRarity(swimsuits),
        recentlyAdded: {
          characters: characters.slice(-5), // Last 5 characters
          swimsuits: swimsuits.slice(-5)   // Last 5 swimsuits
        }
      };

      res.json({
        success: true,
        data: stats,
        message: 'Character statistics retrieved successfully',
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('Error fetching character statistics:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to fetch character statistics',
        errorId: `err_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        timestamp: new Date().toISOString()
      });
    }
  }

  private groupCharactersByMonth(characters: any[]): Record<string, number> {
    const monthCounts: Record<string, number> = {};
    
    characters.forEach(char => {
      if (char.birthday) {
        const month = new Date(char.birthday).getMonth() + 1;
        const monthName = new Date(2024, month - 1).toLocaleString('default', { month: 'long' });
        monthCounts[monthName] = (monthCounts[monthName] || 0) + 1;
      }
    });

    return monthCounts;
  }

  private groupSwimsuitssByRarity(swimsuits: any[]): Record<string, number> {
    const rarityCounts: Record<string, number> = {};
    
    swimsuits.forEach(swimsuit => {
      if (swimsuit.rarity) {
        rarityCounts[swimsuit.rarity] = (rarityCounts[swimsuit.rarity] || 0) + 1;
      }
    });

    return rarityCounts;
  }
}
